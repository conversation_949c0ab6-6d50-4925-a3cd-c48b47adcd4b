#!/bin/bash
#########################################################################################
#                     MULLS multi-LiDAR calibration for intersections                  #
#########################################################################################

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_DIR="${SCRIPT_DIR}/log"
BIN_DIR="${SCRIPT_DIR}/bin"
EXECUTABLE="${BIN_DIR}/mull_reg_all"

# Create log directory if it doesn't exist
mkdir -p "${LOG_DIR}"

# Check if executable exists
if [ ! -f "${EXECUTABLE}" ]; then
    echo "Error: Executable not found at ${EXECUTABLE}"
    echo "Please build the project first using: cd build && make"
    exit 1
fi

# Default parameters - can be overridden by command line arguments
PCD_DATA_DIR="${SCRIPT_DIR}/poc_data/pcd/"
CONFIG_DIR="${SCRIPT_DIR}/poc_data/conf/"
OUTPUT_DIR="${SCRIPT_DIR}/poc_data/result/"
OUTPUT_MERGED_PCD="merged_calibrated.pcd"
ALIGN_BLIND_SPOT_FIRST=true
REALTIME_VIEWER_ON=false

# Registration parameters (optimized for intersection scenarios)
CLOUD_DOWN_RES=0.05
GF_GRID_SIZE=3.0
GF_IN_GRID_H_THRE=0.3
GF_NEIGH_GRID_H_THRE=1.5
GF_MAX_H=50.0
GF_GROUND_DOWN_RATE=8
GF_NONGROUND_DOWN_RATE=3
DIST_INVERSE_SAMPLING_METHOD=1
UNIT_DIST=50.0
PCA_DISTANCE_ADAPTIVE_ON=false
PCA_NEIGHBOR_RADIUS=1.8
PCA_NEIGHBOR_COUNT=50
LINEARITY_THRE=0.65
PLANARITY_THRE=0.65
CURVATURE_THRE=0.1
CORR_NUM=3000
RECIPROCAL_CORR_ON=true
FIXED_NUM_CORR_ON=false
CORR_DIS_THRE=3.0
REG_MAX_ITER_NUM=2000
CONVERGE_TRAN=0.0005
CONVERGE_ROT_D=0.002
TEASER_ON=true
VIS_INTENSITY_SCALE=256.0

# Screen resolution for viewer
SCREEN_WIDTH=1920
SCREEN_HEIGHT=1080

# Function to print usage
print_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Multi-LiDAR calibration system for intersection scenarios.
Supports directional LiDARs (_LidN, _LidS, _LidE, _LidW) and blind-spot LiDARs (_LidB).

OPTIONS:
    --pcd_data_dir DIR          Directory containing point cloud data files (default: ${PCD_DATA_DIR})
    --config_dir DIR            Directory containing LiDAR configuration files (default: ${CONFIG_DIR})
    --output_dir DIR            Directory for output files (default: ${OUTPUT_DIR})
    --output_merged_pcd FILE    Output merged point cloud filename (default: ${OUTPUT_MERGED_PCD})
    --align_blind_spot_first    Align blind-spot LiDARs with directional LiDARs first (default: ${ALIGN_BLIND_SPOT_FIRST})
    --realtime_viewer_on        Launch real-time registration viewer (default: ${REALTIME_VIEWER_ON})
    --cloud_down_res FLOAT      Voxel size for downsampling (default: ${CLOUD_DOWN_RES})
    --teaser_on                 Use TEASER++ for global registration (default: ${TEASER_ON})
    --help                      Show this help message

CALIBRATION ALGORITHM:
    1. Optional: Align blind-spot LiDARs (_LidB) with their corresponding directional LiDARs
    2. Align opposing directional LiDARs:
       - Align _LidN with _LidS (North-South pair)
       - Align _LidE with _LidW (East-West pair)
    3. Cross-alignment: Align merged point clouds from different directions
    4. Output generation:
       - Update LiDAR world coordinate calibration files in poc_data/conf/
       - Generate merged point cloud PCD files with different colors for each LiDAR

EXAMPLES:
    # Basic calibration with default parameters
    $0

    # Calibration with custom output directory
    $0 --output_dir ./my_results/

    # Calibration with real-time viewer enabled
    $0 --realtime_viewer_on true

    # Calibration with higher resolution downsampling
    $0 --cloud_down_res 0.02

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --pcd_data_dir)
            PCD_DATA_DIR="$2"
            shift 2
            ;;
        --config_dir)
            CONFIG_DIR="$2"
            shift 2
            ;;
        --output_dir)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        --output_merged_pcd)
            OUTPUT_MERGED_PCD="$2"
            shift 2
            ;;
        --align_blind_spot_first)
            ALIGN_BLIND_SPOT_FIRST="$2"
            shift 2
            ;;
        --realtime_viewer_on)
            REALTIME_VIEWER_ON="$2"
            shift 2
            ;;
        --cloud_down_res)
            CLOUD_DOWN_RES="$2"
            shift 2
            ;;
        --teaser_on)
            TEASER_ON="$2"
            shift 2
            ;;
        --help)
            print_usage
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            print_usage
            exit 1
            ;;
    esac
done

# Validate input directories
if [ ! -d "${PCD_DATA_DIR}" ]; then
    echo "Error: PCD data directory not found: ${PCD_DATA_DIR}"
    exit 1
fi

if [ ! -d "${CONFIG_DIR}" ]; then
    echo "Error: Configuration directory not found: ${CONFIG_DIR}"
    exit 1
fi

# Create output directory if it doesn't exist
mkdir -p "${OUTPUT_DIR}"

# Check for LiDAR files
PCD_COUNT=$(find "${PCD_DATA_DIR}" -name "*.pcd" | wc -l)
if [ "${PCD_COUNT}" -eq 0 ]; then
    echo "Error: No PCD files found in ${PCD_DATA_DIR}"
    exit 1
fi

echo "==========================================================================================="
echo "                     MULLS Multi-LiDAR Calibration for Intersections"
echo "==========================================================================================="
echo "PCD Data Directory: ${PCD_DATA_DIR}"
echo "Config Directory: ${CONFIG_DIR}"
echo "Output Directory: ${OUTPUT_DIR}"
echo "Found ${PCD_COUNT} PCD files"
echo "==========================================================================================="

# Run the calibration
"${EXECUTABLE}" \
    --colorlogtostderr=true \
    -stderrthreshold 0 \
    -log_dir "${LOG_DIR}" \
    --v=10 \
    --pcd_data_dir="${PCD_DATA_DIR}" \
    --config_dir="${CONFIG_DIR}" \
    --output_dir="${OUTPUT_DIR}" \
    --output_merged_pcd="${OUTPUT_MERGED_PCD}" \
    --align_blind_spot_first="${ALIGN_BLIND_SPOT_FIRST}" \
    --realtime_viewer_on="${REALTIME_VIEWER_ON}" \
    --screen_width="${SCREEN_WIDTH}" \
    --screen_height="${SCREEN_HEIGHT}" \
    --cloud_down_res="${CLOUD_DOWN_RES}" \
    --gf_grid_size="${GF_GRID_SIZE}" \
    --gf_in_grid_h_thre="${GF_IN_GRID_H_THRE}" \
    --gf_neigh_grid_h_thre="${GF_NEIGH_GRID_H_THRE}" \
    --gf_max_h="${GF_MAX_H}" \
    --gf_ground_down_rate="${GF_GROUND_DOWN_RATE}" \
    --gf_nonground_down_rate="${GF_NONGROUND_DOWN_RATE}" \
    --dist_inverse_sampling_method="${DIST_INVERSE_SAMPLING_METHOD}" \
    --unit_dist="${UNIT_DIST}" \
    --pca_distance_adpative_on="${PCA_DISTANCE_ADAPTIVE_ON}" \
    --pca_neighbor_radius="${PCA_NEIGHBOR_RADIUS}" \
    --pca_neighbor_count="${PCA_NEIGHBOR_COUNT}" \
    --linearity_thre="${LINEARITY_THRE}" \
    --planarity_thre="${PLANARITY_THRE}" \
    --curvature_thre="${CURVATURE_THRE}" \
    --corr_num="${CORR_NUM}" \
    --reciprocal_corr_on="${RECIPROCAL_CORR_ON}" \
    --fixed_num_corr_on="${FIXED_NUM_CORR_ON}" \
    --corr_dis_thre="${CORR_DIS_THRE}" \
    --reg_max_iter_num="${REG_MAX_ITER_NUM}" \
    --converge_tran="${CONVERGE_TRAN}" \
    --converge_rot_d="${CONVERGE_ROT_D}" \
    --teaser_on="${TEASER_ON}" \
    --vis_intensity_scale="${VIS_INTENSITY_SCALE}"

# Check exit status
EXIT_CODE=$?
if [ ${EXIT_CODE} -eq 0 ]; then
    echo "==========================================================================================="
    echo "                              Calibration Completed Successfully!"
    echo "==========================================================================================="
    echo "Results saved in: ${OUTPUT_DIR}"
    echo "Updated configuration files in: ${CONFIG_DIR}"
    echo "Merged point cloud: ${OUTPUT_DIR}${OUTPUT_MERGED_PCD}"
    echo "==========================================================================================="
else
    echo "==========================================================================================="
    echo "                                 Calibration Failed!"
    echo "==========================================================================================="
    echo "Exit code: ${EXIT_CODE}"
    echo "Check log files in: ${LOG_DIR}"
    echo "==========================================================================================="
fi

exit ${EXIT_CODE}
