#!/usr/bin/env python3
"""
Multi-LiDAR Calibration System - Streamlit Web Interface
Interactive web interface for multi-LiDAR calibration in intersection scenarios.
Supports directional LiDARs (_LidN, _LidS, _LidE, _LidW) and blind-spot LiDARs (_LidB).

Author: Augment Agent
Based on MULLS framework by <PERSON><PERSON>
"""

import streamlit as st
import subprocess
import os
import glob
import json
import time
from pathlib import Path
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from typing import Dict, List, Tuple, Optional
import re

# Page configuration
st.set_page_config(
    page_title="Multi-LiDAR Calibration System",
    page_icon="🚗",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Constants
SCRIPT_DIR = Path(__file__).parent
DEFAULT_PCD_DIR = SCRIPT_DIR / "poc_data" / "pcd"
DEFAULT_CONFIG_DIR = SCRIPT_DIR / "poc_data" / "conf"
DEFAULT_OUTPUT_DIR = SCRIPT_DIR / "poc_data" / "result"
EXECUTABLE_PATH = SCRIPT_DIR / "bin" / "mull_reg_all"
LAUNCH_SCRIPT = SCRIPT_DIR / "mulls_reg_all.sh"

class LidarCalibrationUI:
    def __init__(self):
        self.init_session_state()
        
    def init_session_state(self):
        """Initialize session state variables"""
        if 'calibration_running' not in st.session_state:
            st.session_state.calibration_running = False
        if 'calibration_results' not in st.session_state:
            st.session_state.calibration_results = None
        if 'discovered_lidars' not in st.session_state:
            st.session_state.discovered_lidars = []
            
    def discover_lidars(self, pcd_dir: Path, config_dir: Path) -> List[Dict]:
        """Discover LiDAR files and their configurations"""
        lidars = []
        
        if not pcd_dir.exists():
            return lidars
            
        for pcd_file in pcd_dir.glob("*.pcd"):
            filename = pcd_file.stem
            config_file = config_dir / f"{filename}.cfg"
            
            # Determine LiDAR type
            lidar_type = "Unknown"
            if "_LidN" in filename:
                lidar_type = "Directional North"
            elif "_LidS" in filename:
                lidar_type = "Directional South"
            elif "_LidE" in filename:
                lidar_type = "Directional East"
            elif "_LidW" in filename:
                lidar_type = "Directional West"
            elif "_LidB" in filename:
                lidar_type = "Blind Spot"
                
            # Extract position (A, B, C, D)
            position_match = re.search(r'R\d+_([ABCD])[a-z]_Lid[NSEWB]', filename)
            position = position_match.group(1) if position_match else "Unknown"
            
            # Get file sizes
            pcd_size = pcd_file.stat().st_size / (1024 * 1024)  # MB
            config_exists = config_file.exists()
            config_size = config_file.stat().st_size if config_exists else 0
            
            lidars.append({
                'name': filename,
                'type': lidar_type,
                'position': position,
                'pcd_file': str(pcd_file),
                'config_file': str(config_file),
                'pcd_size_mb': round(pcd_size, 2),
                'config_exists': config_exists,
                'config_size': config_size
            })
            
        return sorted(lidars, key=lambda x: (x['position'], x['type']))
    
    def render_header(self):
        """Render the main header"""
        st.title("🚗 Multi-LiDAR Calibration System")
        st.markdown("""
        Interactive calibration system for intersection scenarios with directional and blind-spot LiDARs.
        
        **Supported LiDAR Types:**
        - **Directional LiDARs**: `_LidN` (North), `_LidS` (South), `_LidE` (East), `_LidW` (West)
        - **Blind-spot LiDARs**: `_LidB`
        """)
        
    def render_sidebar(self) -> Dict:
        """Render sidebar with configuration options"""
        st.sidebar.header("⚙️ Configuration")
        
        # Directory settings
        st.sidebar.subheader("📁 Directories")
        pcd_dir = st.sidebar.text_input(
            "PCD Data Directory", 
            value=str(DEFAULT_PCD_DIR),
            help="Directory containing point cloud data files (.pcd)"
        )
        
        config_dir = st.sidebar.text_input(
            "Config Directory",
            value=str(DEFAULT_CONFIG_DIR), 
            help="Directory containing LiDAR configuration files (.cfg)"
        )
        
        output_dir = st.sidebar.text_input(
            "Output Directory",
            value=str(DEFAULT_OUTPUT_DIR),
            help="Directory for output files"
        )
        
        # Calibration settings
        st.sidebar.subheader("🎯 Calibration Settings")
        align_blind_spot_first = st.sidebar.checkbox(
            "Align blind-spot LiDARs first",
            value=True,
            help="Align blind-spot LiDARs with directional LiDARs before opposing pairs"
        )
        
        realtime_viewer = st.sidebar.checkbox(
            "Enable real-time viewer",
            value=False,
            help="Launch real-time registration viewer (requires display)"
        )
        
        # Registration parameters
        st.sidebar.subheader("🔧 Registration Parameters")
        cloud_down_res = st.sidebar.slider(
            "Downsampling Resolution (m)",
            min_value=0.01,
            max_value=0.2,
            value=0.05,
            step=0.01,
            help="Voxel size for point cloud downsampling"
        )
        
        corr_dis_thre = st.sidebar.slider(
            "Correspondence Distance Threshold (m)",
            min_value=1.0,
            max_value=10.0,
            value=3.0,
            step=0.5,
            help="Distance threshold for correspondence matching"
        )
        
        max_iterations = st.sidebar.slider(
            "Maximum Iterations",
            min_value=100,
            max_value=5000,
            value=2000,
            step=100,
            help="Maximum iterations for registration"
        )
        
        use_teaser = st.sidebar.checkbox(
            "Use TEASER++ for global registration",
            value=True,
            help="Use TEASER++ instead of RANSAC for global registration"
        )
        
        return {
            'pcd_dir': Path(pcd_dir),
            'config_dir': Path(config_dir),
            'output_dir': Path(output_dir),
            'align_blind_spot_first': align_blind_spot_first,
            'realtime_viewer': realtime_viewer,
            'cloud_down_res': cloud_down_res,
            'corr_dis_thre': corr_dis_thre,
            'max_iterations': max_iterations,
            'use_teaser': use_teaser
        }
    
    def render_lidar_overview(self, config: Dict):
        """Render LiDAR discovery and overview"""
        st.header("📡 LiDAR Discovery")
        
        # Discover LiDARs
        if st.button("🔍 Scan for LiDAR Files", type="primary"):
            with st.spinner("Scanning directories..."):
                st.session_state.discovered_lidars = self.discover_lidars(
                    config['pcd_dir'], config['config_dir']
                )
        
        if st.session_state.discovered_lidars:
            st.success(f"Found {len(st.session_state.discovered_lidars)} LiDAR files")
            
            # Create DataFrame for display
            df = pd.DataFrame(st.session_state.discovered_lidars)
            
            # Display summary statistics
            col1, col2, col3, col4 = st.columns(4)
            with col1:
                st.metric("Total LiDARs", len(df))
            with col2:
                directional_count = len(df[df['type'].str.contains('Directional')])
                st.metric("Directional LiDARs", directional_count)
            with col3:
                blind_spot_count = len(df[df['type'] == 'Blind Spot'])
                st.metric("Blind Spot LiDARs", blind_spot_count)
            with col4:
                total_size = df['pcd_size_mb'].sum()
                st.metric("Total Data Size", f"{total_size:.1f} MB")
            
            # Display detailed table
            st.subheader("📋 LiDAR Details")
            display_df = df[['name', 'type', 'position', 'pcd_size_mb', 'config_exists']].copy()
            display_df.columns = ['Name', 'Type', 'Position', 'Size (MB)', 'Config Available']
            st.dataframe(display_df, use_container_width=True)
            
            # Visualization
            self.render_lidar_visualization(df)
            
        else:
            st.info("Click 'Scan for LiDAR Files' to discover available LiDAR data")
    
    def render_lidar_visualization(self, df: pd.DataFrame):
        """Render LiDAR visualization charts"""
        col1, col2 = st.columns(2)
        
        with col1:
            # LiDAR type distribution
            type_counts = df['type'].value_counts()
            fig_type = px.pie(
                values=type_counts.values,
                names=type_counts.index,
                title="LiDAR Type Distribution"
            )
            st.plotly_chart(fig_type, use_container_width=True)
        
        with col2:
            # Position distribution
            position_counts = df['position'].value_counts()
            fig_pos = px.bar(
                x=position_counts.index,
                y=position_counts.values,
                title="LiDAR Position Distribution",
                labels={'x': 'Position', 'y': 'Count'}
            )
            st.plotly_chart(fig_pos, use_container_width=True)
    
    def run_calibration(self, config: Dict) -> bool:
        """Run the calibration process"""
        if not LAUNCH_SCRIPT.exists():
            st.error(f"Launch script not found: {LAUNCH_SCRIPT}")
            return False
            
        # Build command
        cmd = [
            str(LAUNCH_SCRIPT),
            f"--pcd_data_dir={config['pcd_dir']}",
            f"--config_dir={config['config_dir']}",
            f"--output_dir={config['output_dir']}",
            f"--align_blind_spot_first={str(config['align_blind_spot_first']).lower()}",
            f"--realtime_viewer_on={str(config['realtime_viewer']).lower()}",
            f"--cloud_down_res={config['cloud_down_res']}",
            f"--corr_dis_thre={config['corr_dis_thre']}",
            f"--reg_max_iter_num={config['max_iterations']}",
            f"--teaser_on={str(config['use_teaser']).lower()}"
        ]
        
        try:
            # Create output directory
            config['output_dir'].mkdir(parents=True, exist_ok=True)
            
            # Run calibration
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=3600  # 1 hour timeout
            )
            
            return result.returncode == 0, result.stdout, result.stderr
            
        except subprocess.TimeoutExpired:
            return False, "", "Calibration timed out after 1 hour"
        except Exception as e:
            return False, "", str(e)
    
    def render_calibration_control(self, config: Dict):
        """Render calibration control panel"""
        st.header("🚀 Calibration Control")
        
        if not st.session_state.discovered_lidars:
            st.warning("Please scan for LiDAR files first")
            return
            
        # Pre-flight checks
        st.subheader("✅ Pre-flight Checks")
        checks_passed = True
        
        col1, col2 = st.columns(2)
        with col1:
            if config['pcd_dir'].exists():
                st.success("✓ PCD directory exists")
            else:
                st.error("✗ PCD directory not found")
                checks_passed = False
                
            if config['config_dir'].exists():
                st.success("✓ Config directory exists")
            else:
                st.error("✗ Config directory not found")
                checks_passed = False
        
        with col2:
            if LAUNCH_SCRIPT.exists():
                st.success("✓ Launch script found")
            else:
                st.error("✗ Launch script not found")
                checks_passed = False
                
            if len(st.session_state.discovered_lidars) >= 2:
                st.success(f"✓ {len(st.session_state.discovered_lidars)} LiDAR files ready")
            else:
                st.error("✗ Need at least 2 LiDAR files")
                checks_passed = False
        
        # Calibration button
        if checks_passed and not st.session_state.calibration_running:
            if st.button("🎯 Start Calibration", type="primary", use_container_width=True):
                st.session_state.calibration_running = True
                st.rerun()
        elif st.session_state.calibration_running:
            st.info("🔄 Calibration in progress...")
            
            # Run calibration
            with st.spinner("Running multi-LiDAR calibration..."):
                success, stdout, stderr = self.run_calibration(config)
                
            st.session_state.calibration_running = False
            
            if success:
                st.success("🎉 Calibration completed successfully!")
                st.session_state.calibration_results = {
                    'success': True,
                    'output_dir': str(config['output_dir']),
                    'stdout': stdout,
                    'stderr': stderr
                }
            else:
                st.error("❌ Calibration failed!")
                st.session_state.calibration_results = {
                    'success': False,
                    'stdout': stdout,
                    'stderr': stderr
                }
                
            st.rerun()
        else:
            st.error("❌ Pre-flight checks failed. Please fix the issues above.")
    
    def render_results(self):
        """Render calibration results"""
        if not st.session_state.calibration_results:
            return
            
        st.header("📊 Calibration Results")
        
        results = st.session_state.calibration_results
        
        if results['success']:
            st.success("✅ Calibration completed successfully!")
            
            # Check for output files
            output_dir = Path(results['output_dir'])
            if output_dir.exists():
                output_files = list(output_dir.glob("*.pcd"))
                if output_files:
                    st.subheader("📁 Generated Files")
                    for file in output_files:
                        file_size = file.stat().st_size / (1024 * 1024)
                        st.write(f"- `{file.name}` ({file_size:.2f} MB)")
                        
        else:
            st.error("❌ Calibration failed!")
            
        # Show logs
        with st.expander("📝 Detailed Logs"):
            if results['stdout']:
                st.subheader("Standard Output")
                st.code(results['stdout'], language='text')
            if results['stderr']:
                st.subheader("Standard Error")
                st.code(results['stderr'], language='text')
    
    def run(self):
        """Main application entry point"""
        self.render_header()
        
        # Get configuration from sidebar
        config = self.render_sidebar()
        
        # Main content
        tab1, tab2, tab3 = st.tabs(["📡 LiDAR Discovery", "🚀 Calibration", "📊 Results"])
        
        with tab1:
            self.render_lidar_overview(config)
            
        with tab2:
            self.render_calibration_control(config)
            
        with tab3:
            self.render_results()

if __name__ == "__main__":
    app = LidarCalibrationUI()
    app.run()
