# Multi-LiDAR Calibration System for Intersection Scenarios

## Introduction

This is an enhanced implementation of the MULLS (Multi-metric Linear Least Square ICP) framework, specifically designed for multi-LiDAR calibration in intersection scenarios. The system supports both directional LiDARs (_LidN, _LidS, _LidE, _LidW) and blind-spot LiDARs (_LidB) commonly deployed at traffic intersections.

## Features

- **Multi-LiDAR Calibration**: Automated calibration system for intersection scenarios
- **Directional LiDAR Support**: North, South, East, West directional LiDARs
- **Blind-spot LiDAR Support**: Specialized handling for blind-spot LiDARs
- **Intelligent Calibration Algorithm**: 
  1. Optional blind-spot to directional LiDAR alignment
  2. Opposing directional LiDAR alignment (N-S, E-W pairs)
  3. Cross-alignment between merged directional pairs
- **Configuration Management**: Automatic update of LiDAR world coordinate calibration files
- **Visualization**: Color-coded merged point clouds for different LiDARs
- **Web Interface**: Interactive Streamlit-based frontend for easy operation
- **Robust Registration**: TEASER++ and MULLS-ICP integration

## System Architecture

### Core Components

1. **`test/mulls_reg_all.cpp`**: Main C++ calibration program
2. **`mulls_reg_all.sh`**: Shell script launcher with parameter management
3. **`streamlit_calibration_ui.py`**: Interactive web interface

### Data Structure

- **Point Cloud Data**: `poc_data/pcd/` - LiDAR point cloud files (.pcd)
- **Configuration Files**: `poc_data/conf/` - Protobuf configuration files (.cfg)
- **Output Results**: `poc_data/result/` - Calibrated point clouds and merged results

### LiDAR Naming Convention

- **Directional LiDARs**: `R1_Xx_LidN/S/E/W` (North/South/East/West)
- **Blind-spot LiDARs**: `R1_Xx_LidB`
- **Position Identifiers**: A, B, C, D (intersection corners)

## Dependencies

### Core Dependencies
- PCL (Point Cloud Library) >= 1.7
- Eigen3
- glog
- gflags
- OpenMP
- Boost
- Protocol Buffers (protobuf)
- TEASER++ (optional, for robust global registration)

### Python Dependencies (for web interface)
- streamlit >= 1.28.0
- pandas >= 1.5.0
- plotly >= 5.15.0
- numpy >= 1.21.0

## Installation

### 1. Build Core System

```bash
# Install dependencies (Ubuntu/Debian)
sudo apt-get update
sudo apt-get install -y \
    libpcl-dev \
    libeigen3-dev \
    libgoogle-glog-dev \
    libgflags-dev \
    libboost-all-dev \
    libprotobuf-dev \
    protobuf-compiler

# Build the system
mkdir build
cd build
cmake ..
make -j4
```

### 2. Install Python Dependencies

```bash
pip install -r requirements.txt
```

## Usage

### Method 1: Command Line Interface

```bash
# Basic calibration with default parameters
./mulls_reg_all.sh

# Custom configuration
./mulls_reg_all.sh \
    --pcd_data_dir ./my_data/pcd/ \
    --config_dir ./my_data/conf/ \
    --output_dir ./my_results/ \
    --cloud_down_res 0.02 \
    --teaser_on true

# Show help
./mulls_reg_all.sh --help
```

### Method 2: Web Interface

```bash
# Launch Streamlit web interface
streamlit run streamlit_calibration_ui.py

# Access via browser at http://localhost:8501
```

## Calibration Algorithm

The system implements a sophisticated 4-step calibration algorithm:

### Step 1: Blind-spot Alignment (Optional)
- Aligns blind-spot LiDARs (`_LidB`) with their corresponding directional LiDARs
- Performed per intersection position (A, B, C, D)

### Step 2: Opposing Directional Alignment
- **North-South Alignment**: Registers `_LidN` with `_LidS`
- **East-West Alignment**: Registers `_LidE` with `_LidW`

### Step 3: Cross-alignment
- Merges opposing directional pairs into unified point clouds
- Performs cross-registration between merged N-S and E-W clouds
- Ensures global consistency across all directions

### Step 4: Output Generation
- Updates LiDAR configuration files with calibrated transformations
- Generates color-coded merged point cloud for visualization
- Saves individual calibrated point clouds

## Configuration Parameters

### Key Parameters

| Parameter | Default | Description |
|-----------|---------|-------------|
| `cloud_down_res` | 0.05 | Voxel size for downsampling (meters) |
| `corr_dis_thre` | 3.0 | Distance threshold for correspondences |
| `reg_max_iter_num` | 2000 | Maximum registration iterations |
| `teaser_on` | true | Use TEASER++ for global registration |
| `align_blind_spot_first` | true | Align blind-spot LiDARs first |

### Directory Structure

```
mulls_lidar_calib/
├── poc_data/
│   ├── pcd/           # Point cloud files
│   ├── conf/          # Configuration files  
│   └── result/        # Output results
├── test/
│   └── mulls_reg_all.cpp
├── mulls_reg_all.sh
├── streamlit_calibration_ui.py
└── requirements.txt
```

## Output Files

### Calibrated Point Clouds
- `merged_calibrated.pcd`: Color-coded merged point cloud
- `{lidar_name}_calibrated.pcd`: Individual calibrated point clouds

### Updated Configurations
- Updated `.cfg` files with calibrated transformations in `poc_data/conf/`

## Web Interface Features

### 📡 LiDAR Discovery
- Automatic scanning of PCD and configuration directories
- Visual overview of discovered LiDARs by type and position
- File size and availability status

### 🚀 Calibration Control
- Pre-flight system checks
- Interactive parameter configuration
- Real-time calibration progress monitoring

### 📊 Results Visualization
- Calibration success/failure status
- Generated file listings
- Detailed execution logs

## Troubleshooting

### Common Issues

1. **Build Errors**: Ensure all dependencies are installed
2. **Missing LiDAR Files**: Check PCD directory path and file naming convention
3. **Configuration Errors**: Verify protobuf configuration file format
4. **Registration Failures**: Adjust correspondence thresholds and iteration limits

### Debug Mode

```bash
# Enable verbose logging
./mulls_reg_all.sh --v=10

# Check log files
tail -f log/mulls_reg_all.*
```

## Citation

If you use this work in your research, please cite:

```bibtex
@article{pan2021mulls,
  title={MULLS: Versatile LiDAR SLAM via Multi-metric Linear Least Square},
  author={Pan, Yue and Xiao, Pengchuan and He, Yujie and Shao, Zhenlei and Li, Zesong},
  journal={arXiv preprint arXiv:2102.03771},
  year={2021}
}
```

## License

This project is based on the MULLS framework and follows the same licensing terms.
