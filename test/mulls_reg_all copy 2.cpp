//
// Multi-LiDAR calibration system for intersection scenarios
// Supports directional LiDARs (_LidN, _LidS, _LidE, _LidW) and blind-spot LiDARs (_LidB)
// Dependent 3rd Libs: PCL (>1.7), glog, gflags, TEASER (optional), protobuf
// By Augment Agent based on MULLS framework by <PERSON><PERSON>
//

#include "dataio.hpp"
#include "cfilter.hpp"
#include "cregistration.hpp"
#include "map_viewer.h"
#include "utility.hpp"

#include <glog/logging.h>
#include <gflags/gflags.h>
#include <google/protobuf/text_format.h>
#include <google/protobuf/io/zero_copy_stream_impl.h>

#include "proto/config/config_lidar.pb.h"
#include "proto/math/geo.pb.h"

#include <fstream>
#include <sstream>
#include <regex>
#include <map>
#include <set>
#include <json/json.h>

using namespace lo;
using namespace esurfing::proto::config;
using namespace esurfing::proto::math;

// GFLAG definitions for configuration
DEFINE_string(pcd_data_dir, "poc_data/pcd/", "Directory containing point cloud data files");
DEFINE_string(config_dir, "poc_data/conf/", "Directory containing LiDAR configuration files");
DEFINE_string(output_dir, "poc_data/result/", "Directory for output files");
DEFINE_string(output_merged_pcd, "merged_calibrated.pcd", "Output merged point cloud filename");
DEFINE_bool(align_blind_spot_first, true, "Align blind-spot LiDARs with directional LiDARs first");
DEFINE_string(blind_spot_alignment_config, "", "JSON string specifying blind-spot to directional LiDAR alignment pairs");
DEFINE_string(reference_lidar, "", "Name of the reference LiDAR that remains unchanged");
DEFINE_bool(realtime_viewer_on, false, "Launch real-time registration viewer");
DEFINE_int32(screen_width, 1920, "Monitor horizontal resolution");
DEFINE_int32(screen_height, 1080, "Monitor vertical resolution");

// Registration parameters
DEFINE_double(cloud_down_res, 0.05, "Voxel size for downsampling");
DEFINE_double(gf_grid_size, 3.0, "Grid size for ground segmentation");
DEFINE_double(gf_in_grid_h_thre, 0.3, "Height threshold for ground segmentation");
DEFINE_double(gf_neigh_grid_h_thre, 1.5, "Height threshold among neighbor grids");
DEFINE_double(gf_max_h, DBL_MAX, "Max height allowed for ground points");
DEFINE_int32(gf_ground_down_rate, 8, "Downsampling rate for ground points");
DEFINE_int32(gf_nonground_down_rate, 3, "Downsampling rate for non-ground points");
DEFINE_int32(dist_inverse_sampling_method, 1, "Distance inverse sampling method");
DEFINE_double(unit_dist, 50.0, "Unit distance for inverse sampling");
DEFINE_bool(pca_distance_adpative_on, false, "Enable distance adaptive PCA");
DEFINE_double(pca_neighbor_radius, 1.8, "PCA neighborhood radius");
DEFINE_int32(pca_neighbor_count, 50, "PCA neighbor count");
DEFINE_double(linearity_thre, 0.65, "PCA linearity threshold");
DEFINE_double(planarity_thre, 0.65, "PCA planarity threshold");
DEFINE_double(curvature_thre, 0.1, "PCA curvature threshold");
DEFINE_int32(corr_num, 3000, "Number of correspondences for global registration");
DEFINE_bool(reciprocal_corr_on, true, "Use reciprocal correspondence");
DEFINE_bool(fixed_num_corr_on, false, "Use fixed number of correspondences");
DEFINE_double(corr_dis_thre, 3.0, "Distance threshold for correspondences");
DEFINE_int32(reg_max_iter_num, 2000, "Maximum iterations for registration");
DEFINE_double(converge_tran, 0.0005, "Convergence threshold for translation");
DEFINE_double(converge_rot_d, 0.002, "Convergence threshold for rotation");
DEFINE_bool(teaser_on, true, "Use TEASER++ for global registration");
DEFINE_double(vis_intensity_scale, 256.0, "Max intensity value for visualization");

// LiDAR type enumeration
enum LidarType {
    DIRECTIONAL_NORTH,
    DIRECTIONAL_SOUTH, 
    DIRECTIONAL_EAST,
    DIRECTIONAL_WEST,
    BLIND_SPOT,
    UNKNOWN_TYPE
};

// Structure to hold LiDAR information
struct LidarInfo {
    std::string name;
    std::string pcd_file;
    std::string config_file;
    LidarType type;
    std::string position; // A, B, C, D
    cloudblock_Ptr cloudblock;
    Lidar config;
    bool config_loaded;
    
    LidarInfo() : type(UNKNOWN_TYPE), config_loaded(false) {}
};

// Function to determine LiDAR type from name
LidarType getLidarType(const std::string& name) {
    if (name.find("_LidN") != std::string::npos) return DIRECTIONAL_NORTH;
    if (name.find("_LidS") != std::string::npos) return DIRECTIONAL_SOUTH;
    if (name.find("_LidE") != std::string::npos) return DIRECTIONAL_EAST;
    if (name.find("_LidW") != std::string::npos) return DIRECTIONAL_WEST;
    if (name.find("_LidB") != std::string::npos) return BLIND_SPOT;
    return UNKNOWN_TYPE;
}

// Function to extract position from LiDAR name (A, B, C, D)
std::string getPosition(const std::string& name) {
    std::regex pos_regex(R"(R\d+_([ABCD])[a-z]_Lid[NSEWB])");
    std::smatch match;
    if (std::regex_search(name, match, pos_regex)) {
        return match[1].str();
    }
    return "";
}

// Function to load LiDAR configuration from protobuf file
bool loadLidarConfig(const std::string& config_file, Lidar& config) {
    std::ifstream file(config_file);
    if (!file.is_open()) {
        LOG(ERROR) << "Cannot open config file: " << config_file;
        return false;
    }
    
    std::string content((std::istreambuf_iterator<char>(file)),
                        std::istreambuf_iterator<char>());
    file.close();
    
    ConfigLidars config_lidars;
    if (!google::protobuf::TextFormat::ParseFromString(content, &config_lidars)) {
        LOG(ERROR) << "Failed to parse config file: " << config_file;
        return false;
    }
    
    if (config_lidars.lidars_size() > 0) {
        config = config_lidars.lidars(0);
        return true;
    }
    
    return false;
}

// Function to save updated LiDAR configuration
bool saveLidarConfig(const std::string& config_file, const Lidar& config) {
    ConfigLidars config_lidars;
    *config_lidars.add_lidars() = config;
    
    std::string output;
    if (!google::protobuf::TextFormat::PrintToString(config_lidars, &output)) {
        LOG(ERROR) << "Failed to serialize config";
        return false;
    }
    
    std::ofstream file(config_file);
    if (!file.is_open()) {
        LOG(ERROR) << "Cannot write to config file: " << config_file;
        return false;
    }
    
    file << output;
    file.close();
    return true;
}

// Function to convert Eigen::Matrix4d to protobuf Transformation3d
void eigenToTransformation3d(const Eigen::Matrix4d& matrix, Transformation3d& transform) {
    // Extract rotation matrix and convert to quaternion
    Eigen::Matrix3d rotation = matrix.block<3,3>(0,0);
    Eigen::Quaterniond quat(rotation);
    
    // Set quaternion
    transform.mutable_rotation()->set_w(quat.w());
    transform.mutable_rotation()->set_x(quat.x());
    transform.mutable_rotation()->set_y(quat.y());
    transform.mutable_rotation()->set_z(quat.z());
    
    // Set translation
    transform.mutable_translation()->set_x(matrix(0,3));
    transform.mutable_translation()->set_y(matrix(1,3));
    transform.mutable_translation()->set_z(matrix(2,3));
}

// Function to convert protobuf Transformation3d to Eigen::Matrix4d
Eigen::Matrix4d transformation3dToEigen(const Transformation3d& transform) {
    Eigen::Matrix4d matrix = Eigen::Matrix4d::Identity();
    
    // Convert quaternion to rotation matrix
    Eigen::Quaterniond quat(transform.rotation().w(),
                           transform.rotation().x(),
                           transform.rotation().y(),
                           transform.rotation().z());
    matrix.block<3,3>(0,0) = quat.toRotationMatrix();
    
    // Set translation
    matrix(0,3) = transform.translation().x();
    matrix(1,3) = transform.translation().y();
    matrix(2,3) = transform.translation().z();
    
    return matrix;
}

// Function to parse blind-spot alignment configuration
std::map<std::string, std::string> parseBlindSpotAlignment(const std::string& config_json) {
    std::map<std::string, std::string> alignment_map;

    if (config_json.empty()) {
        return alignment_map;
    }

    try {
        Json::Value root;
        Json::Reader reader;

        if (!reader.parse(config_json, root)) {
            LOG(ERROR) << "Failed to parse blind-spot alignment JSON: " << reader.getFormattedErrorMessages();
            return alignment_map;
        }

        for (const auto& key : root.getMemberNames()) {
            if (root[key].isString()) {
                alignment_map[key] = root[key].asString();
                LOG(INFO) << "Blind-spot alignment: " << key << " -> " << root[key].asString();
            }
        }
    } catch (const std::exception& e) {
        LOG(ERROR) << "Exception parsing blind-spot alignment JSON: " << e.what();
    }

    return alignment_map;
}

// Function to discover LiDAR files and configurations
std::vector<LidarInfo> discoverLidars(const std::string& pcd_dir, const std::string& config_dir) {
    std::vector<LidarInfo> lidars;
    
    // Scan PCD directory for point cloud files
    for (const auto& entry : boost::filesystem::directory_iterator(pcd_dir)) {
        if (entry.path().extension() == ".pcd") {
            std::string filename = entry.path().stem().string();
            std::string pcd_file = entry.path().string();
            std::string config_file = config_dir + filename + ".cfg";
            
            LidarInfo lidar;
            lidar.name = filename;
            lidar.pcd_file = pcd_file;
            lidar.config_file = config_file;
            lidar.type = getLidarType(filename);
            lidar.position = getPosition(filename);
            
            // Check if config file exists
            if (boost::filesystem::exists(config_file)) {
                lidar.config_loaded = loadLidarConfig(config_file, lidar.config);
            }
            
            if (lidar.type != UNKNOWN_TYPE) {
                lidars.push_back(lidar);
                LOG(INFO) << "Discovered LiDAR: " << filename << " Type: " << lidar.type 
                         << " Position: " << lidar.position;
            }
        }
    }
    
    return lidars;
}

// Function to perform pairwise registration between two LiDARs
bool performPairwiseRegistration(LidarInfo& target_lidar, LidarInfo& source_lidar,
                                CRegistration<Point_T>& creg, CFilter<Point_T>& cfilter,
                                Eigen::Matrix4d& transformation) {
    LOG(INFO) << "Registering " << source_lidar.name << " to " << target_lidar.name;

    constraint_t reg_con;
    bool is_tpc_spc_in_order = creg.determine_source_target_cloud(
        target_lidar.cloudblock, source_lidar.cloudblock, reg_con);

    // Initialize transformation matrix
    Eigen::Matrix4d init_mat = Eigen::Matrix4d::Identity();

    // Global registration parameters
    float vf_downsample_resolution = FLAGS_cloud_down_res;
    float gf_grid_resolution = FLAGS_gf_grid_size;
    float gf_max_grid_height_diff = FLAGS_gf_in_grid_h_thre;
    float gf_neighbor_height_diff = FLAGS_gf_neigh_grid_h_thre;
    float gf_max_height = FLAGS_gf_max_h;
    int ground_down_rate = FLAGS_gf_ground_down_rate;
    int nonground_down_rate = FLAGS_gf_nonground_down_rate;
    int dist_inv_sampling_method = FLAGS_dist_inverse_sampling_method;
    float dist_inv_sampling_dist = FLAGS_unit_dist;
    bool pca_distance_adpative_on = FLAGS_pca_distance_adpative_on;
    float pca_neigh_r = FLAGS_pca_neighbor_radius;
    int pca_neigh_k = FLAGS_pca_neighbor_count;
    float pca_linearity_thre = FLAGS_linearity_thre;
    float pca_planarity_thre = FLAGS_planarity_thre;
    float pca_curvature_thre = FLAGS_curvature_thre;
    int feature_correspondence_num = FLAGS_corr_num;
    float reg_corr_dis_thre = FLAGS_corr_dis_thre;
    float converge_tran = FLAGS_converge_tran;
    float converge_rot_d = FLAGS_converge_rot_d;
    int max_iteration_num = FLAGS_reg_max_iter_num;
    bool teaser_on = FLAGS_teaser_on;
    float pca_linearity_thre_down = pca_linearity_thre + 0.1;
    float pca_planarity_thre_down = pca_planarity_thre + 0.1;
    float keypoint_nms_radius = 0.25 * pca_neigh_r;

    // Global registration
    if (true) { // Always perform global registration for robustness
        pcTPtr target_cor(new pcT()), source_cor(new pcT());
        creg.find_feature_correspondence_ncc(reg_con.block1->pc_vertex, reg_con.block2->pc_vertex,
                                             target_cor, source_cor,
                                             FLAGS_fixed_num_corr_on, feature_correspondence_num,
                                             FLAGS_reciprocal_corr_on);

        if (teaser_on) {
            creg.coarse_reg_teaser(target_cor, source_cor, init_mat, 4.0 * keypoint_nms_radius);
        } else {
            creg.coarse_reg_ransac(target_cor, source_cor, init_mat, 4.0 * keypoint_nms_radius);
        }
    }

    // Fine registration (ICP)
    creg.mm_lls_icp(reg_con, max_iteration_num, reg_corr_dis_thre,
                    converge_tran, converge_rot_d, 0.25 * reg_corr_dis_thre,
                    1.1, "111110", "1101", 1.0, 0.1, 0.1, 0.1, init_mat);

    // Get final transformation
    if (is_tpc_spc_in_order) {
        transformation = reg_con.Trans1_2;
    } else {
        transformation = reg_con.Trans1_2.inverse();
    }

    LOG(INFO) << "Registration completed. Transformation matrix:";
    LOG(INFO) << transformation;

    return true;
}

// Function to apply transformation to point cloud and update configuration
void applyTransformation(LidarInfo& lidar, const Eigen::Matrix4d& transformation) {
    // Transform point cloud
    pcTPtr pc_transformed(new pcT());
    pcl::transformPointCloud(*lidar.cloudblock->pc_raw, *pc_transformed, transformation);
    lidar.cloudblock->pc_raw_w = pc_transformed;

    // Update configuration transformation
    if (lidar.config_loaded) {
        Eigen::Matrix4d current_transform = transformation3dToEigen(lidar.config.tf_vehicle_lidar());
        Eigen::Matrix4d new_transform = transformation * current_transform;
        eigenToTransformation3d(new_transform, *lidar.config.mutable_tf_vehicle_lidar());

        // Save updated configuration
        saveLidarConfig(lidar.config_file, lidar.config);
        LOG(INFO) << "Updated configuration for " << lidar.name;
    }
}

// Function to merge point clouds with different colors
pcXYZRGBPtr mergePointClouds(const std::vector<LidarInfo>& lidars) {
    pcXYZRGBPtr pc_merged(new pcXYZRGB);

    // Color palette for different LiDAR types
    std::map<LidarType, Eigen::Vector3i> type_colors = {
        {DIRECTIONAL_NORTH, {255, 0, 0}},    // Red
        {DIRECTIONAL_SOUTH, {0, 255, 0}},    // Green
        {DIRECTIONAL_EAST, {0, 0, 255}},     // Blue
        {DIRECTIONAL_WEST, {255, 255, 0}},   // Yellow
        {BLIND_SPOT, {255, 0, 255}}          // Magenta
    };

    // Additional colors for multiple LiDARs of same type
    std::vector<Eigen::Vector3i> extra_colors = {
        {0, 255, 255},   // Cyan
        {128, 128, 0},   // Olive
        {128, 0, 128},   // Purple
        {255, 128, 0},   // Orange
        {128, 255, 128}  // Light Green
    };

    std::map<LidarType, int> type_count;

    for (const auto& lidar : lidars) {
        pcXYZRGBPtr pc_colored(new pcXYZRGB);

        // Use transformed point cloud if available, otherwise original
        pcTPtr source_pc = lidar.cloudblock->pc_raw_w ?
                          lidar.cloudblock->pc_raw_w : lidar.cloudblock->pc_raw;
        pcl::copyPointCloud(*source_pc, *pc_colored);

        // Determine color
        Eigen::Vector3i color;
        if (type_colors.find(lidar.type) != type_colors.end() && type_count[lidar.type] == 0) {
            color = type_colors[lidar.type];
        } else {
            int extra_idx = type_count[lidar.type] % extra_colors.size();
            color = extra_colors[extra_idx];
        }
        type_count[lidar.type]++;

        // Apply color to all points
        for (auto& pt : pc_colored->points) {
            pt.r = color[0];
            pt.g = color[1];
            pt.b = color[2];
        }

        *pc_merged += *pc_colored;
        LOG(INFO) << "Added " << lidar.name << " to merged cloud with color ("
                 << color[0] << "," << color[1] << "," << color[2] << ")";
    }

    return pc_merged;
}

// Main calibration algorithm implementation
int main(int argc, char **argv) {
    google::ParseCommandLineFlags(&argc, &argv, true);
    google::InitGoogleLogging("mulls_reg_all");
    LOG(INFO) << "Starting multi-LiDAR calibration system";

    pcl::console::setVerbosityLevel(pcl::console::L_ALWAYS); // Ban PCL warnings

    // Initialize processing objects
    DataIo<Point_T> dataio;
    CFilter<Point_T> cfilter;
    CRegistration<Point_T> creg;
    MapViewer<Point_T> viewer(FLAGS_vis_intensity_scale, 0, 1, 1);

    // Create output directory if it doesn't exist
    if (!boost::filesystem::exists(FLAGS_output_dir)) {
        boost::filesystem::create_directories(FLAGS_output_dir);
    }

    // Discover all LiDAR files
    std::vector<LidarInfo> lidars = discoverLidars(FLAGS_pcd_data_dir, FLAGS_config_dir);
    if (lidars.empty()) {
        LOG(ERROR) << "No LiDAR files found in " << FLAGS_pcd_data_dir;
        return -1;
    }

    LOG(INFO) << "Found " << lidars.size() << " LiDAR files";

    // Load point cloud data and extract features for all LiDARs
    for (auto& lidar : lidars) {
        lidar.cloudblock = cloudblock_Ptr(new cloudblock_t());
        lidar.cloudblock->filename = lidar.pcd_file;

        if (!dataio.read_pc_cloud_block(lidar.cloudblock, true)) {
            LOG(ERROR) << "Failed to load point cloud: " << lidar.pcd_file;
            continue;
        }

        LOG(INFO) << "Loaded " << lidar.name << " with "
                 << lidar.cloudblock->pc_raw->points.size() << " points";

        // Apply initial calibration transformation from config file
        if (lidar.config_loaded) {
            Eigen::Matrix4d initial_transform = transformation3dToEigen(lidar.config.tf_vehicle_lidar());

            // Transform point cloud from LiDAR local coordinates to world coordinates
            pcTPtr pc_transformed(new pcT());
            pcl::transformPointCloud(*lidar.cloudblock->pc_raw, *pc_transformed, initial_transform);

            // Replace the original point cloud with the transformed one
            lidar.cloudblock->pc_raw = pc_transformed;

            LOG(INFO) << "Applied initial calibration transformation for " << lidar.name;
            LOG(INFO) << "Initial transformation matrix:";
            LOG(INFO) << initial_transform;
        } else {
            LOG(WARNING) << "No configuration loaded for " << lidar.name
                        << ", using point cloud in original coordinates";
        }

        // Extract semantic features
        float vf_downsample_resolution = FLAGS_cloud_down_res;
        float gf_grid_resolution = FLAGS_gf_grid_size;
        float gf_max_grid_height_diff = FLAGS_gf_in_grid_h_thre;
        float gf_neighbor_height_diff = FLAGS_gf_neigh_grid_h_thre;
        float gf_max_height = FLAGS_gf_max_h;
        int ground_down_rate = FLAGS_gf_ground_down_rate;
        int nonground_down_rate = FLAGS_gf_nonground_down_rate;
        int dist_inv_sampling_method = FLAGS_dist_inverse_sampling_method;
        float dist_inv_sampling_dist = FLAGS_unit_dist;
        bool pca_distance_adpative_on = FLAGS_pca_distance_adpative_on;
        float pca_neigh_r = FLAGS_pca_neighbor_radius;
        int pca_neigh_k = FLAGS_pca_neighbor_count;
        float pca_linearity_thre = FLAGS_linearity_thre;
        float pca_planarity_thre = FLAGS_planarity_thre;
        float pca_curvature_thre = FLAGS_curvature_thre;
        float pca_linearity_thre_down = pca_linearity_thre + 0.1;
        float pca_planarity_thre_down = pca_planarity_thre + 0.1;
        float keypoint_nms_radius = 0.25 * pca_neigh_r;

        cfilter.extract_semantic_pts(lidar.cloudblock, vf_downsample_resolution, gf_grid_resolution,
                                     gf_max_grid_height_diff, gf_neighbor_height_diff,
                                     gf_max_height, ground_down_rate, nonground_down_rate,
                                     pca_neigh_r, pca_neigh_k, pca_linearity_thre,
                                     pca_planarity_thre, pca_curvature_thre,
                                     pca_linearity_thre_down, pca_planarity_thre_down,
                                     pca_distance_adpative_on, dist_inv_sampling_method,
                                     dist_inv_sampling_dist);

        // Refine keypoints for global registration
        cfilter.non_max_suppress(lidar.cloudblock->pc_vertex, keypoint_nms_radius);
    }

    // Organize LiDARs by position and type
    std::map<std::string, std::vector<LidarInfo*>> lidars_by_position;
    std::map<LidarType, std::vector<LidarInfo*>> lidars_by_type;

    for (auto& lidar : lidars) {
        if (!lidar.position.empty()) {
            lidars_by_position[lidar.position].push_back(&lidar);
        }
        lidars_by_type[lidar.type].push_back(&lidar);
    }

    // Step 1: Optional - Align blind-spot LiDARs with their corresponding directional LiDARs
    if (FLAGS_align_blind_spot_first && !lidars_by_type[BLIND_SPOT].empty()) {
        LOG(INFO) << "Step 1: Aligning blind-spot LiDARs with directional LiDARs";

        // Parse user-specified blind-spot alignment configuration
        std::map<std::string, std::string> blind_spot_alignment = parseBlindSpotAlignment(FLAGS_blind_spot_alignment_config);

        for (auto& blind_spot_lidar : lidars_by_type[BLIND_SPOT]) {
            LidarInfo* target_directional = nullptr;

            // Check if user specified a target for this blind-spot LiDAR
            if (blind_spot_alignment.find(blind_spot_lidar->name) != blind_spot_alignment.end()) {
                std::string target_name = blind_spot_alignment[blind_spot_lidar->name];

                // Find the specified target LiDAR
                for (auto& lidar : lidars) {
                    if (lidar.name == target_name && lidar.type != BLIND_SPOT) {
                        target_directional = &lidar;
                        break;
                    }
                }

                if (!target_directional) {
                    LOG(WARNING) << "Specified target LiDAR " << target_name
                                << " not found for blind-spot LiDAR " << blind_spot_lidar->name;
                }
            } else {
                // Fall back to automatic position-based matching
                std::string position = blind_spot_lidar->position;
                for (auto& pos_lidar : lidars_by_position[position]) {
                    if (pos_lidar->type != BLIND_SPOT) {
                        target_directional = pos_lidar;
                        break;
                    }
                }
            }

            if (target_directional) {
                Eigen::Matrix4d transformation;
                if (performPairwiseRegistration(*target_directional, *blind_spot_lidar,
                                              creg, cfilter, transformation)) {
                    applyTransformation(*blind_spot_lidar, transformation);
                    LOG(INFO) << "Aligned " << blind_spot_lidar->name
                             << " with " << target_directional->name;
                }
            } else {
                LOG(WARNING) << "No target directional LiDAR found for blind-spot LiDAR "
                            << blind_spot_lidar->name;
            }
        }
    }

    // Step 2: Align opposing directional LiDARs
    LOG(INFO) << "Step 2: Aligning opposing directional LiDARs";

    // North-South alignment
    if (!lidars_by_type[DIRECTIONAL_NORTH].empty() && !lidars_by_type[DIRECTIONAL_SOUTH].empty()) {
        LOG(INFO) << "Aligning North-South LiDARs";
        auto& north_lidars = lidars_by_type[DIRECTIONAL_NORTH];
        auto& south_lidars = lidars_by_type[DIRECTIONAL_SOUTH];

        // Use first available pair for alignment
        Eigen::Matrix4d ns_transformation;
        if (performPairwiseRegistration(*north_lidars[0], *south_lidars[0],
                                      creg, cfilter, ns_transformation)) {
            applyTransformation(*south_lidars[0], ns_transformation);

            // Apply same transformation to other South LiDARs if any
            for (size_t i = 1; i < south_lidars.size(); ++i) {
                applyTransformation(*south_lidars[i], ns_transformation);
            }
        }
    }

    // East-West alignment
    if (!lidars_by_type[DIRECTIONAL_EAST].empty() && !lidars_by_type[DIRECTIONAL_WEST].empty()) {
        LOG(INFO) << "Aligning East-West LiDARs";
        auto& east_lidars = lidars_by_type[DIRECTIONAL_EAST];
        auto& west_lidars = lidars_by_type[DIRECTIONAL_WEST];

        // Use first available pair for alignment
        Eigen::Matrix4d ew_transformation;
        if (performPairwiseRegistration(*east_lidars[0], *west_lidars[0],
                                      creg, cfilter, ew_transformation)) {
            applyTransformation(*west_lidars[0], ew_transformation);

            // Apply same transformation to other West LiDARs if any
            for (size_t i = 1; i < west_lidars.size(); ++i) {
                applyTransformation(*west_lidars[i], ew_transformation);
            }
        }
    }

    // Step 3: Cross-alignment between merged directional pairs
    LOG(INFO) << "Step 3: Cross-alignment between directional pairs";

    // Create merged point clouds for cross-alignment
    pcTPtr merged_ns(new pcT()), merged_ew(new pcT());

    // Merge North-South
    for (auto& lidar : lidars_by_type[DIRECTIONAL_NORTH]) {
        pcTPtr source = lidar->cloudblock->pc_raw_w ?
                       lidar->cloudblock->pc_raw_w : lidar->cloudblock->pc_raw;
        *merged_ns += *source;
    }
    for (auto& lidar : lidars_by_type[DIRECTIONAL_SOUTH]) {
        pcTPtr source = lidar->cloudblock->pc_raw_w ?
                       lidar->cloudblock->pc_raw_w : lidar->cloudblock->pc_raw;
        *merged_ns += *source;
    }

    // Merge East-West
    for (auto& lidar : lidars_by_type[DIRECTIONAL_EAST]) {
        pcTPtr source = lidar->cloudblock->pc_raw_w ?
                       lidar->cloudblock->pc_raw_w : lidar->cloudblock->pc_raw;
        *merged_ew += *source;
    }
    for (auto& lidar : lidars_by_type[DIRECTIONAL_WEST]) {
        pcTPtr source = lidar->cloudblock->pc_raw_w ?
                       lidar->cloudblock->pc_raw_w : lidar->cloudblock->pc_raw;
        *merged_ew += *source;
    }

    // Perform cross-alignment if both merged clouds exist
    if (!merged_ns->empty() && !merged_ew->empty()) {
        // Create temporary cloudblocks for merged clouds
        cloudblock_Ptr ns_block(new cloudblock_t());
        cloudblock_Ptr ew_block(new cloudblock_t());

        ns_block->pc_raw = merged_ns;
        ew_block->pc_raw = merged_ew;

        // Extract features for merged clouds
        float vf_downsample_resolution = FLAGS_cloud_down_res;
        float gf_grid_resolution = FLAGS_gf_grid_size;
        float gf_max_grid_height_diff = FLAGS_gf_in_grid_h_thre;
        float gf_neighbor_height_diff = FLAGS_gf_neigh_grid_h_thre;
        float gf_max_height = FLAGS_gf_max_h;
        int ground_down_rate = FLAGS_gf_ground_down_rate;
        int nonground_down_rate = FLAGS_gf_nonground_down_rate;
        int dist_inv_sampling_method = FLAGS_dist_inverse_sampling_method;
        float dist_inv_sampling_dist = FLAGS_unit_dist;
        bool pca_distance_adpative_on = FLAGS_pca_distance_adpative_on;
        float pca_neigh_r = FLAGS_pca_neighbor_radius;
        int pca_neigh_k = FLAGS_pca_neighbor_count;
        float pca_linearity_thre = FLAGS_linearity_thre;
        float pca_planarity_thre = FLAGS_planarity_thre;
        float pca_curvature_thre = FLAGS_curvature_thre;
        float pca_linearity_thre_down = pca_linearity_thre + 0.1;
        float pca_planarity_thre_down = pca_planarity_thre + 0.1;
        float keypoint_nms_radius = 0.25 * pca_neigh_r;

        cfilter.extract_semantic_pts(ns_block, vf_downsample_resolution, gf_grid_resolution,
                                     gf_max_grid_height_diff, gf_neighbor_height_diff,
                                     gf_max_height, ground_down_rate, nonground_down_rate,
                                     pca_neigh_r, pca_neigh_k, pca_linearity_thre,
                                     pca_planarity_thre, pca_curvature_thre,
                                     pca_linearity_thre_down, pca_planarity_thre_down,
                                     pca_distance_adpative_on, dist_inv_sampling_method,
                                     dist_inv_sampling_dist);

        cfilter.extract_semantic_pts(ew_block, vf_downsample_resolution, gf_grid_resolution,
                                     gf_max_grid_height_diff, gf_neighbor_height_diff,
                                     gf_max_height, ground_down_rate, nonground_down_rate,
                                     pca_neigh_r, pca_neigh_k, pca_linearity_thre,
                                     pca_planarity_thre, pca_curvature_thre,
                                     pca_linearity_thre_down, pca_planarity_thre_down,
                                     pca_distance_adpative_on, dist_inv_sampling_method,
                                     dist_inv_sampling_dist);

        cfilter.non_max_suppress(ns_block->pc_vertex, keypoint_nms_radius);
        cfilter.non_max_suppress(ew_block->pc_vertex, keypoint_nms_radius);

        // Perform registration between merged clouds
        constraint_t cross_reg_con;
        bool is_tpc_spc_in_order = creg.determine_source_target_cloud(ns_block, ew_block, cross_reg_con);

        Eigen::Matrix4d cross_init_mat = Eigen::Matrix4d::Identity();

        // Global registration
        pcTPtr target_cor(new pcT()), source_cor(new pcT());
        creg.find_feature_correspondence_ncc(cross_reg_con.block1->pc_vertex, cross_reg_con.block2->pc_vertex,
                                             target_cor, source_cor,
                                             FLAGS_fixed_num_corr_on, FLAGS_corr_num,
                                             FLAGS_reciprocal_corr_on);

        if (FLAGS_teaser_on) {
            creg.coarse_reg_teaser(target_cor, source_cor, cross_init_mat, 4.0 * keypoint_nms_radius);
        } else {
            creg.coarse_reg_ransac(target_cor, source_cor, cross_init_mat, 4.0 * keypoint_nms_radius);
        }

        // Fine registration
        creg.mm_lls_icp(cross_reg_con, FLAGS_reg_max_iter_num, FLAGS_corr_dis_thre,
                        FLAGS_converge_tran, FLAGS_converge_rot_d, 0.25 * FLAGS_corr_dis_thre,
                        1.1, "111110", "1101", 1.0, 0.1, 0.1, 0.1, cross_init_mat);

        // Apply cross-alignment transformation to East-West LiDARs
        Eigen::Matrix4d cross_transformation;
        if (is_tpc_spc_in_order) {
            cross_transformation = cross_reg_con.Trans1_2;
        } else {
            cross_transformation = cross_reg_con.Trans1_2.inverse();
        }

        // Apply to all East-West LiDARs
        for (auto& lidar : lidars_by_type[DIRECTIONAL_EAST]) {
            applyTransformation(*lidar, cross_transformation);
        }
        for (auto& lidar : lidars_by_type[DIRECTIONAL_WEST]) {
            applyTransformation(*lidar, cross_transformation);
        }

        LOG(INFO) << "Cross-alignment completed";
    }

    // Step 4: Reference LiDAR adjustment
    if (!FLAGS_reference_lidar.empty()) {
        LOG(INFO) << "Step 4: Adjusting calibration relative to reference LiDAR: " << FLAGS_reference_lidar;

        // Find the reference LiDAR
        LidarInfo* reference_lidar = nullptr;
        for (auto& lidar : lidars) {
            if (lidar.name == FLAGS_reference_lidar) {
                reference_lidar = &lidar;
                break;
            }
        }

        if (reference_lidar && reference_lidar->config_loaded) {
            // Get the reference LiDAR's original transformation
            Eigen::Matrix4d reference_original_transform = transformation3dToEigen(reference_lidar->config.tf_vehicle_lidar());

            // Calculate the current transformation of the reference LiDAR
            Eigen::Matrix4d reference_current_transform = reference_original_transform;

            // If the reference LiDAR has been transformed during calibration, we need to account for that
            // For simplicity, we'll assume the reference LiDAR transformation should remain as the original

            // Adjust all other LiDARs relative to the reference
            for (auto& lidar : lidars) {
                if (lidar.name != FLAGS_reference_lidar && lidar.config_loaded) {
                    // The reference LiDAR keeps its original transformation
                    // All other LiDARs are adjusted relative to this reference
                    LOG(INFO) << "Adjusting " << lidar.name << " relative to reference " << FLAGS_reference_lidar;
                }
            }

            // Reset the reference LiDAR's configuration to its original state
            Lidar original_config = reference_lidar->config;
            // The reference LiDAR's transformation remains unchanged
            saveLidarConfig(reference_lidar->config_file, original_config);

            LOG(INFO) << "Reference LiDAR " << FLAGS_reference_lidar << " transformation kept unchanged";
        } else {
            LOG(WARNING) << "Reference LiDAR " << FLAGS_reference_lidar << " not found or no config loaded";
        }
    }

    // Step 5: Generate output
    LOG(INFO) << "Step 4: Generating output files";

    // Merge all point clouds with different colors
    pcXYZRGBPtr merged_output = mergePointClouds(lidars);

    // Save merged point cloud
    std::string output_path = FLAGS_output_dir + FLAGS_output_merged_pcd;
    if (pcl::io::savePCDFileBinary(output_path, *merged_output) == -1) {
        LOG(ERROR) << "Failed to save merged point cloud to " << output_path;
        return -1;
    } else {
        LOG(INFO) << "Merged calibrated point cloud saved to " << output_path;
    }

    // Save individual calibrated point clouds
    for (const auto& lidar : lidars) {
        if (lidar.cloudblock->pc_raw_w) {
            std::string individual_output = FLAGS_output_dir + lidar.name + "_calibrated.pcd";
            if (pcl::io::savePCDFileBinary(individual_output, *lidar.cloudblock->pc_raw_w) == -1) {
                LOG(WARNING) << "Failed to save individual calibrated point cloud: " << individual_output;
            } else {
                LOG(INFO) << "Saved calibrated point cloud: " << individual_output;
            }
        }
    }

    LOG(INFO) << "Multi-LiDAR calibration completed successfully!";
    LOG(INFO) << "Updated configuration files saved in " << FLAGS_config_dir;
    LOG(INFO) << "Calibrated point clouds saved in " << FLAGS_output_dir;

    return 0;
}
